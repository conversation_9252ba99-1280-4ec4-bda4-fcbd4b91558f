/**
 * Content Management System Library
 *
 * This module provides utilities for reading, parsing, and managing MDX content
 * with frontmatter support, type safety, and validation.
 */

import {
    BlogPost,
    BlogPostMetadata,
    ContentConfig,
    ContentError,
    ContentMetadata,
    ContentNotFoundError,
    ContentQueryOptions,
    ContentStats,
    ContentType,
    ContentValidationError,
    ContentValidationResult,
    Locale,
    Page,
    PageMetadata,
    ParsedContent,
} from '@/types/content';
import fs from 'fs';
import matter from 'gray-matter';
import path from 'path';

// Content directory paths
const CONTENT_DIR = path.join(process.cwd(), 'content');
const POSTS_DIR = path.join(CONTENT_DIR, 'posts');
const PAGES_DIR = path.join(CONTENT_DIR, 'pages');
const CONFIG_DIR = path.join(CONTENT_DIR, 'config');

// Security constants
const CONTENT_ROOT = path.resolve(process.cwd(), 'content');
const ALLOWED_EXTENSIONS = ['.md', '.mdx', '.json'];
const SEO_LIMITS = {
  MAX_TITLE_LENGTH: 60,
  MAX_DESCRIPTION_LENGTH: 160,
} as const;

// Security functions
function validatePath(filePath: string): boolean {
  const resolvedPath = path.resolve(filePath);
  return resolvedPath.startsWith(CONTENT_ROOT);
}

function validateExtension(filePath: string): boolean {
  const ext = path.extname(filePath);
  return ALLOWED_EXTENSIONS.includes(ext);
}

function safeReadFileSync(filePath: string): string {
  if (!validatePath(filePath)) {
    throw new Error(`Invalid file path: ${filePath}`);
  }
  return fs.readFileSync(filePath, 'utf-8');
}

function safeExistsSync(filePath: string): boolean {
  if (!validatePath(filePath)) {
    return false;
  }
  return fs.existsSync(filePath);
}

function safeReaddirSync(dirPath: string): string[] {
  if (!validatePath(dirPath)) {
    return [];
  }
  return fs.readdirSync(dirPath);
}

function safeAssignProperty(obj: Record<string, unknown>, key: string, value: unknown): void {
  if (typeof key === 'string' && key.length > 0) {
    obj[key] = value;
  }
}

// Default content configuration
const DEFAULT_CONFIG: ContentConfig = {
  defaultLocale: 'en',
  supportedLocales: ['en', 'zh'],
  postsPerPage: 10,
  enableDrafts: process.env.NODE_ENV === 'development',
  enableSearch: true,
  autoGenerateExcerpt: true,
  excerptLength: SEO_LIMITS.MAX_DESCRIPTION_LENGTH,
  dateFormat: 'YYYY-MM-DD',
  timeZone: 'UTC',
};

/**
 * Get content configuration
 */
export function getContentConfig(): ContentConfig {
  try {
    const configPath = path.join(CONFIG_DIR, 'content.json');
    if (safeExistsSync(configPath)) {
      const configFile = safeReadFileSync(configPath);
      const userConfig = JSON.parse(configFile);
      return { ...DEFAULT_CONFIG, ...userConfig };
    }
  } catch (error) {
    console.warn('Failed to load content config, using defaults:', error);
  }
  return DEFAULT_CONFIG;
}

/**
 * Validate content metadata
 */
export function validateContentMetadata(
  metadata: any,
  type: ContentType,
): ContentValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  if (!metadata.title) {
    errors.push('Title is required');
  }
  if (!metadata.slug) {
    errors.push('Slug is required');
  }
  if (!metadata.publishedAt) {
    errors.push('Published date is required');
  }

  // Date validation
  if (metadata.publishedAt && isNaN(Date.parse(metadata.publishedAt))) {
    errors.push('Invalid published date format');
  }
  if (metadata.updatedAt && isNaN(Date.parse(metadata.updatedAt))) {
    errors.push('Invalid updated date format');
  }

  // Type-specific validation
  if (type === 'posts') {
    if (!metadata.author) {
      warnings.push('Author is recommended for blog posts');
    }
    if (!metadata.description && !metadata.excerpt) {
      warnings.push('Description or excerpt is recommended for SEO');
    }
  }

  // SEO validation
  if (metadata.seo) {
    if (metadata.seo.title && metadata.seo.title.length > SEO_LIMITS.MAX_TITLE_LENGTH) {
      warnings.push(`SEO title should be under ${SEO_LIMITS.MAX_TITLE_LENGTH} characters`);
    }
    if (metadata.seo.description && metadata.seo.description.length > SEO_LIMITS.MAX_DESCRIPTION_LENGTH) {
      warnings.push(`SEO description should be under ${SEO_LIMITS.MAX_DESCRIPTION_LENGTH} characters`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Parse MDX file with frontmatter
 */
export function parseContentFile<T extends ContentMetadata = ContentMetadata>(
  filePath: string,
  type: ContentType,
): ParsedContent<T> {
  try {
    const fileContent = safeReadFileSync(filePath);
    const { data: metadata, content, excerpt } = matter(fileContent, {
      excerpt: true,
    });

    // Generate slug from filename if not provided
    const fileName = path.basename(filePath, path.extname(filePath));
    // eslint-disable-next-line dot-notation
    const slug = (metadata as Record<string, unknown>)['slug'] as string || fileName;

    // Validate metadata
    const validation = validateContentMetadata(metadata, type);
    if (!validation.isValid) {
      throw new ContentValidationError(
        `Invalid content metadata in ${filePath}`,
        validation.errors,
        filePath,
      );
    }

    // Log warnings
    if (validation.warnings.length > 0) {
      console.warn(`Content warnings for ${filePath}:`, validation.warnings);
    }

    return {
      metadata: { ...metadata, slug } as T,
      content,
      excerpt: excerpt || '',
      slug,
      filePath,
    };
  } catch (error) {
    if (error instanceof ContentValidationError) {
      throw error;
    }
    throw new ContentError(
      `Failed to parse content file: ${filePath}`,
      'PARSE_ERROR',
      filePath,
    );
  }
}

/**
 * Get all content files in a directory
 */
export function getContentFiles(
  contentDir: string,
  locale?: Locale,
): string[] {
  const targetDir = locale ? path.join(contentDir, locale) : contentDir;

  if (!safeExistsSync(targetDir)) {
    return [];
  }

  return safeReaddirSync(targetDir)
    .filter((file) => file.endsWith('.md') || file.endsWith('.mdx'))
    .map((file) => path.join(targetDir, file));
}

/**
 * Get all blog posts
 */
export function getAllPosts(
  locale?: Locale,
  options: ContentQueryOptions = {},
): BlogPost[] {
  const files = getContentFiles(POSTS_DIR, locale);
  const posts = files
    .map((file) => parseContentFile<BlogPostMetadata>(file, 'posts'))
    .filter((post) => {
      // Filter drafts in production
      if (!getContentConfig().enableDrafts && post.metadata.draft) {
        return false;
      }

      // Apply filters
      if (options.draft !== undefined && post.metadata.draft !== options.draft) {
        return false;
      }
      if (options.featured !== undefined && post.metadata.featured !== options.featured) {
        return false;
      }
      if (options.tags && !options.tags.some(tag => post.metadata.tags?.includes(tag))) {
        return false;
      }
      if (options.categories && !options.categories.some(cat => post.metadata.categories?.includes(cat))) {
        return false;
      }

      return true;
    }) as BlogPost[];

  // Sort posts
  const sortBy = options.sortBy || 'publishedAt';
  const sortOrder = options.sortOrder || 'desc';

  posts.sort((a, b) => {
    const aValue = a.metadata[sortBy] || '';
    const bValue = b.metadata[sortBy] || '';

    if (sortOrder === 'desc') {
      return bValue.localeCompare(aValue);
    }
    return aValue.localeCompare(bValue);
  });

  // Apply pagination
  if (options.offset || options.limit) {
    const start = options.offset || 0;
    const end = options.limit ? start + options.limit : undefined;
    return posts.slice(start, end);
  }

  return posts;
}

/**
 * Get all pages
 */
export function getAllPages(locale?: Locale): Page[] {
  const files = getContentFiles(PAGES_DIR, locale);
  return files
    .map((file) => parseContentFile<PageMetadata>(file, 'pages'))
    .filter((page) => {
      // Filter drafts in production
      if (!getContentConfig().enableDrafts && page.metadata.draft) {
        return false;
      }
      return true;
    }) as Page[];
}

/**
 * Get content by slug
 */
export function getContentBySlug<T extends ContentMetadata = ContentMetadata>(
  slug: string,
  type: ContentType,
  locale?: Locale,
): ParsedContent<T> {
  const contentDir = type === 'posts' ? POSTS_DIR : PAGES_DIR;
  const files = getContentFiles(contentDir, locale);

  for (const file of files) {
    try {
      const content = parseContentFile<T>(file, type);
      if (content.slug === slug) {
        return content;
      }
    } catch (error) {
      console.warn(`Failed to parse ${file}:`, error);
      continue;
    }
  }

  throw new ContentNotFoundError(slug, locale);
}

/**
 * Get blog post by slug
 */
export function getPostBySlug(slug: string, locale?: Locale): BlogPost {
  return getContentBySlug<BlogPostMetadata>(slug, 'posts', locale) as BlogPost;
}

/**
 * Get page by slug
 */
export function getPageBySlug(slug: string, locale?: Locale): Page {
  return getContentBySlug<PageMetadata>(slug, 'pages', locale) as Page;
}

/**
 * Get content statistics
 */
export function getContentStats(): ContentStats {
  const config = getContentConfig();
  const stats: ContentStats = {
    totalPosts: 0,
    totalPages: 0,
    postsByLocale: {} as Record<Locale, number>,
    pagesByLocale: {} as Record<Locale, number>,
    totalTags: 0,
    totalCategories: 0,
    lastUpdated: new Date().toISOString(),
  };

  // Initialize locale counters
  config.supportedLocales.forEach((locale) => {
    stats.postsByLocale[locale] = 0;
    stats.pagesByLocale[locale] = 0;
  });

  // Count posts by locale
  config.supportedLocales.forEach((locale) => {
    try {
      const posts = getAllPosts(locale);
      stats.postsByLocale[locale] = posts.length;
      stats.totalPosts += posts.length;
    } catch (error) {
      console.warn(`Failed to count posts for locale ${locale}:`, error);
    }
  });

  // Count pages by locale
  config.supportedLocales.forEach((locale) => {
    try {
      const pages = getAllPages(locale);
      stats.pagesByLocale[locale] = pages.length;
      stats.totalPages += pages.length;
    } catch (error) {
      console.warn(`Failed to count pages for locale ${locale}:`, error);
    }
  });

  // Count unique tags and categories
  const allTags = new Set<string>();
  const allCategories = new Set<string>();

  config.supportedLocales.forEach((locale) => {
    try {
      const posts = getAllPosts(locale);
      posts.forEach((post) => {
        post.metadata.tags?.forEach((tag) => allTags.add(tag));
        post.metadata.categories?.forEach((cat) => allCategories.add(cat));
      });
    } catch (error) {
      console.warn(`Failed to collect tags/categories for locale ${locale}:`, error);
    }
  });

  stats.totalTags = allTags.size;
  stats.totalCategories = allCategories.size;

  return stats;
}
